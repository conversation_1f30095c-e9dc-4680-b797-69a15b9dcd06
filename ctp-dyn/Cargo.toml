[package]
name = "ctp2rs"
version = "0.1.7"
edition = "2021"
authors = ["Pseudo <<EMAIL>>"]
description = "Shanghai Futures CTP/CTP-Mini/CTP-Sopt Rust native binding, supporting production, evaluation, stock-option, OpenCTP, and LocalCTP versions, compatible with Linux and macOS"
license = "Apache-2.0"
readme = "README.md"
repository = "https://github.com/pseudocodes/ctp2rs/tree/master/ctp-dyn"
keywords = ["ctp", "ctp-rs", "ctp-binding", "quant", "trader"]
categories = ["api-bindings", "finance"]

exclude = [
    "*.log",
    "*.tmp",
    "scripts/*",
    "*.con",
    "*.framework",
    "*.so",
    "*.dll",
    "*.lib",
]
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
clang-sys = { version = "1.8.1", features = ["clang_16_0", "runtime"] }
clang = { version = "2.0.0", features = ["clang_10_0"] }
bindgen = "0.71.0"
regex = "1.11.1"
Inflector = "0.11.4"
roxmltree = "0.20.0"
encoding_rs = "0.8.35"

[dependencies]
libloading = "0.8.6"
memchr = "2.7.4"
futures = "0.3.31"
encoding_rs = "0.8.35"

[features]
default = ["v1alpha1", "dynlib", "ctp_v6_7_2"]
dynlib = []
sopt = []
mini = []
openctp = []
ctp_v6_7_2 = ["dynlib"]
ctp_v6_7_7 = ["dynlib"]
ctp_v6_7_8 = ["dynlib"]
ctp_v6_7_9 = ["dynlib"]
mini_v1_6_9 = ["dynlib", 'mini']
mini_v1_7_0 = ["dynlib", 'mini']
sopt_v3_7_3 = ["dynlib", "sopt"]
v1alpha1 = []
v1alpha2 = []
