/////////////////////////////////////////////////////////////////////////
///@system 新一代交易所系统
///@company 上海期货信息技术有限公司
///@file ThostFtdcTraderApi.h
///@brief 定义了客户端接口
///@history
/// 20060106	赵鸿昊		创建该文件
/////////////////////////////////////////////////////////////////////////

#if !defined(THOST_FTDCTRADERAPI_H)
#define THOST_FTDCTRADERAPI_H

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ThostFtdcUserApiStruct.h"

#ifdef TRADER_API_EXPORT
#ifdef WINDOWS
#define TRADER_API_DLL_EXPORT __declspec(dllexport)
#else
#define TRADER_API_DLL_EXPORT __attribute__((visibility("default")))
#endif
#else
#define TRADER_API_DLL_EXPORT
#endif

// 查询应答错误码描述如下
#define QRY_ERROR_NONE 0
#define QRY_ERROR_INVALID_LOGIN -1
#define QRY_ERROR_DUPLICATE_LOGIN 2
#define QRY_ERROR_NOT_LOGIN_YET -3
#define QRY_ERROR_NO_PRIVILEGE -4 // 无此权限：可能是功能未开启
#define QRY_ERROR_REQUEST -6 // 查询错误：查询过快，尚有未完成的查询任务

class CThostFtdcTraderSpi {
public:
    /// 当客户端与交易后台建立起通信连接时（还未登录前），该方法被调用。
    virtual void OnFrontConnected() { };

    /// 当客户端与交易后台通信连接断开时，该方法被调用。当发生这个情况后，API会自动重新连接，客户端可不做处理。
    ///@param nReason 错误原因
    ///         -3	关闭连接
    ///         -4	网络读失败
    ///         -5	网络写失败
    ///         -6	读订阅流水请求出错
    ///         -7	序列号错误
    ///         -8	读心跳出错
    ///         -9	错误的网络包大小
    virtual void OnFrontDisconnected(int nReason) { };

    /// 心跳超时警告。当长时间未收到报文时，该方法被调用。
    ///@param nTimeLapse 距离上次接收报文的时间
    virtual void OnHeartBeatWarning(int nTimeLapse) { };

    /// 订阅流控警告应答
    virtual void OnRspSubscribeFlowCtrlWarning(CThostFtdcSpecificTraderField* pRspSubscribeTraderField, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 取消订阅流控警告应答
    virtual void OnRspUnSubscribeFlowCtrlWarning(CThostFtdcSpecificTraderField* pRspSubscribeTraderField, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 客户端认证响应
    virtual void OnRspAuthenticate(CThostFtdcRspAuthenticateField* pRspAuthenticateField, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 登录请求响应
    virtual void OnRspUserLogin(CThostFtdcRspUserLoginField* pRspUserLogin, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 登出请求响应
    virtual void OnRspUserLogout(CThostFtdcUserLogoutField* pUserLogout, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 报单录入请求响应
    virtual void OnRspOrderInsert(CThostFtdcInputOrderField* pInputOrder, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 报单操作请求响应
    virtual void OnRspOrderAction(CThostFtdcInputOrderActionField* pInputOrderAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 做市商批量报单操作请求响应
    virtual void OnRspMKBatchOrderAction(CThostFtdcMKInputOrderActionField* pMKInputOrderAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 执行宣告录入请求响应
    virtual void OnRspExecOrderInsert(CThostFtdcInputExecOrderField* pInputExecOrder, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 执行宣告操作请求响应
    virtual void OnRspExecOrderAction(CThostFtdcInputExecOrderActionField* pInputExecOrderAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 询价录入请求响应
    virtual void OnRspForQuoteInsert(CThostFtdcInputForQuoteField* pInputForQuote, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 报价录入请求响应
    virtual void OnRspQuoteInsert(CThostFtdcInputQuoteField* pInputQuote, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 报价操作请求响应
    virtual void OnRspQuoteAction(CThostFtdcInputQuoteActionField* pInputQuoteAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 批量报单操作请求响应
    virtual void OnRspBatchOrderAction(CThostFtdcInputBatchOrderActionField* pInputBatchOrderAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 期权自对冲录入请求响应
    virtual void OnRspOptionSelfCloseInsert(CThostFtdcInputOptionSelfCloseField* pInputOptionSelfClose, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 期权自对冲操作请求响应
    virtual void OnRspOptionSelfCloseAction(CThostFtdcInputOptionSelfCloseActionField* pInputOptionSelfCloseAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 申请组合录入请求响应
    virtual void OnRspCombActionInsert(CThostFtdcInputCombActionField* pInputCombAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询报单响应
    virtual void OnRspQryOrder(CThostFtdcOrderField* pOrder, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询成交响应
    virtual void OnRspQryTrade(CThostFtdcTradeField* pTrade, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询投资者持仓响应
    virtual void OnRspQryInvestorPosition(CThostFtdcInvestorPositionField* pInvestorPosition, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询资金账户响应
    virtual void OnRspQryTradingAccount(CThostFtdcTradingAccountField* pTradingAccount, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询投资者响应
    virtual void OnRspQryInvestor(CThostFtdcInvestorField* pInvestor, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询交易编码响应
    virtual void OnRspQryTradingCode(CThostFtdcTradingCodeField* pTradingCode, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询合约保证金率响应
    virtual void OnRspQryInstrumentMarginRate(CThostFtdcInstrumentMarginRateField* pInstrumentMarginRate, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询合约手续费率响应
    virtual void OnRspQryInstrumentCommissionRate(CThostFtdcInstrumentCommissionRateField* pInstrumentCommissionRate, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询交易所响应
    virtual void OnRspQryExchange(CThostFtdcExchangeField* pExchange, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询产品响应
    virtual void OnRspQryProduct(CThostFtdcProductField* pProduct, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询合约响应
    virtual void OnRspQryInstrument(CThostFtdcInstrumentField* pInstrument, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询申请组合合约响应
    virtual void OnRspQryCombInstrument(CThostFtdcCombInstrumentField* pCombInstrument, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询投资者RCAMS组合保证金响应
    virtual void OnRspQryRCAMSInvestorProdMargin(CThostFtdcRCAMSInvestorProdMarginField* pRCAMSInvestorProdMargin, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询RCAMS策略组合持仓响应
    virtual void OnRspQryRCAMSInvestorCombPosition(CThostFtdcRCAMSInvestorCombPositionField* pRCAMSInvestorCombPosition, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询申请组合响应
    virtual void OnRspQryCombAction(CThostFtdcCombActionField* pCombAction, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询组合单腿汇总表响应
    virtual void OnRspQryInvestorPositionForComb(CThostFtdcInvestorPositionForCombField* pForComb, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询行情响应
    virtual void OnRspQryDepthMarketData(CThostFtdcDepthMarketDataField* pDepthMarketData, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询合约状态响应
    virtual void OnRspQryInstrumentStatus(CThostFtdcInstrumentStatusField* pInstrumentStatus, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询投资者持仓明细响应
    virtual void OnRspQryInvestorPositionDetail(CThostFtdcInvestorPositionDetailField* pInvestorPositionDetail, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询交易所保证金率响应
    virtual void OnRspQryExchangeMarginRate(CThostFtdcExchangeMarginRateField* pExchangeMarginRate, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询交易所调整保证金率响应
    virtual void OnRspQryExchangeMarginRateAdjust(CThostFtdcExchangeMarginRateAdjustField* pExchangeMarginRateAdjust, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询期权交易成本响应
    virtual void OnRspQryOptionInstrTradeCost(CThostFtdcOptionInstrTradeCostField* pOptionInstrTradeCost, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询期权合约手续费响应
    virtual void OnRspQryOptionInstrCommRate(CThostFtdcOptionInstrCommRateField* pOptionInstrCommRate, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询执行宣告响应
    virtual void OnRspQryExecOrder(CThostFtdcExecOrderField* pExecOrder, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询询价响应
    virtual void OnRspQryForQuote(CThostFtdcForQuoteField* pForQuote, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询询价价差响应
    virtual void OnRspQryForQuoteParam(CThostFtdcForQuoteParamField* pForQuoteParam, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询投资者SPBM品种明细响应
    virtual void OnRspQryInvestorProdSPBMDetail(CThostFtdcInvestorProdSPBMDetailField* pInvestorProdSPBMDetail, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询投资者SPMM商品群明细响应
    virtual void OnRspQrySPMMInvestorCommodityGroupMargin(CThostFtdcSPMMInvestorCommodityGroupMarginField* pSPMMInvestorCommodityGroupMargin, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询投资者RULE保证金响应
    virtual void OnRspQryRULEInvestorProdMargin(CThostFtdcRULEInvestorProdMarginField* pRULEInvestorProdMargin, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询交易员报盘机响应
    virtual void OnRspQryTraderOffer(CThostFtdcTraderOfferField* pTraderOffer, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询报价响应
    virtual void OnRspQryQuote(CThostFtdcQuoteField* pQuote, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询期权自对冲响应
    virtual void OnRspQryOptionSelfClose(CThostFtdcOptionSelfCloseField* pOptionSelfClose, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 请求查询交易开关设置响应
    virtual void OnRspQryControlParam(CThostFtdcControlParamField* pControlParam, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 错误应答
    virtual void OnRspError(CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 报单通知
    virtual void OnRtnOrder(CThostFtdcOrderField* pOrder) { };

    /// 成交通知
    virtual void OnRtnTrade(CThostFtdcTradeField* pTrade) { };

    /// 报单录入错误回报
    virtual void OnErrRtnOrderInsert(CThostFtdcInputOrderField* pInputOrder, CThostFtdcRspInfoField* pRspInfo) { };

    /// 报单操作错误回报
    virtual void OnErrRtnOrderAction(CThostFtdcOrderActionField* pOrderAction, CThostFtdcRspInfoField* pRspInfo) { };

    /// 合约交易状态通知
    virtual void OnRtnInstrumentStatus(CThostFtdcInstrumentStatusField* pInstrumentStatus) { };

    /// 执行宣告通知
    virtual void OnRtnExecOrder(CThostFtdcExecOrderField* pExecOrder) { };

    /// 执行宣告录入错误回报
    virtual void OnErrRtnExecOrderInsert(CThostFtdcInputExecOrderField* pInputExecOrder, CThostFtdcRspInfoField* pRspInfo) { };

    /// 执行宣告操作错误回报
    virtual void OnErrRtnExecOrderAction(CThostFtdcExecOrderActionField* pExecOrderAction, CThostFtdcRspInfoField* pRspInfo) { };

    /// 询价录入错误回报
    virtual void OnErrRtnForQuoteInsert(CThostFtdcInputForQuoteField* pInputForQuote, CThostFtdcRspInfoField* pRspInfo) { };

    /// 报价通知
    virtual void OnRtnQuote(CThostFtdcQuoteField* pQuote) { };

    /// 报价录入错误回报
    virtual void OnErrRtnQuoteInsert(CThostFtdcInputQuoteField* pInputQuote, CThostFtdcRspInfoField* pRspInfo) { };

    /// 报价操作错误回报
    virtual void OnErrRtnQuoteAction(CThostFtdcQuoteActionField* pQuoteAction, CThostFtdcRspInfoField* pRspInfo) { };

    /// 询价通知
    virtual void OnRtnForQuoteRsp(CThostFtdcForQuoteRspField* pForQuoteRsp) { };

    /// 批量报单操作错误回报
    virtual void OnErrRtnBatchOrderAction(CThostFtdcBatchOrderActionField* pBatchOrderAction, CThostFtdcRspInfoField* pRspInfo) { };

    /// 期权自对冲通知
    virtual void OnRtnOptionSelfClose(CThostFtdcOptionSelfCloseField* pOptionSelfClose) { };

    /// 期权自对冲录入错误回报
    virtual void OnErrRtnOptionSelfCloseInsert(CThostFtdcInputOptionSelfCloseField* pInputOptionSelfClose, CThostFtdcRspInfoField* pRspInfo) { };

    /// 期权自对冲操作错误回报
    virtual void OnErrRtnOptionSelfCloseAction(CThostFtdcOptionSelfCloseActionField* pOptionSelfCloseAction, CThostFtdcRspInfoField* pRspInfo) { };

    /// 申请组合通知
    virtual void OnRtnCombAction(CThostFtdcCombActionField* pCombAction) { };

    /// 请求查询申报费响应
    virtual void OnRspQryInstrumentOrderCommRate(CThostFtdcInstrumentOrderCommRateField* pInstrumentOrderCommRate, CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 交易所席位流控警告
    virtual void OnRtnFlowCtrlWarning(CThostFtdcFlowCtrlWarningField* pFlowCtrlWarning) { };

    /// 订阅资金变动应答
    virtual void OnRspSubscribeFundChange(CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    /// 取消订阅资金变动应答
    virtual void OnRspUnSubscribeFundChange(CThostFtdcRspInfoField* pRspInfo, int nRequestID, bool bIsLast) { };

    // 资金变动推送
    virtual void OnRtnFundChange(CThostFtdcTradingAccountField* pTradingAccount) { };
};

class TRADER_API_DLL_EXPORT CThostFtdcTraderApi {
public:
    /// 创建TraderApi
    ///@param pszFlowPath 存贮订阅信息文件的目录，默认为当前目录
    ///@return 创建出的UserApi
    static CThostFtdcTraderApi* CreateFtdcTraderApi(const char* pszFlowPath = "");

    /// 获取API的版本信息
    ///@retrun 获取到的版本号
    static const char* GetApiVersion();

    /// 删除接口对象本身
    ///@remark 不再使用本接口对象时,调用该函数删除接口对象
    virtual void Release() = 0;

    /// 初始化
    ///@param bContinuous 为true表示线程不休眠
    ///@remark 初始化运行环境,只有调用后,接口才开始工作
    virtual void Init(bool bContinuous = false) = 0;

    /// 等待接口线程结束运行
    ///@return 线程退出代码
    virtual int Join() = 0;

    /// 获取当前交易日
    ///@retrun 获取到的交易日
    ///@remark 只有登录成功后,才能得到正确的交易日
    virtual const char* GetTradingDay() = 0;

    /// 注册前置机网络地址
    ///@param pszFrontAddress：前置机网络地址。
    ///@remark 网络地址的格式为：“protocol://ipaddress:port”，如：”tcp://127.0.0.1:17001”。
    ///@remark “tcp”代表传输协议，“127.0.0.1”代表服务器地址。”17001”代表服务器端口号。
    virtual void RegisterFront(char* pszFrontAddress) = 0;

    /// 注册回调接口
    ///@param pSpi 派生自回调接口类的实例
    virtual void RegisterSpi(CThostFtdcTraderSpi* pSpi) = 0;

    /// 订阅私有流。
    ///@param nResumeType 私有流重传方式
    ///         THOST_TERT_RESTART:从本交易日开始重传
    ///         THOST_TERT_RESUME:从上次收到的续传
    ///         THOST_TERT_QUICK:只传送登录后私有流的内容
    ///@remark 该方法要在Init方法前调用。若不调用则不会收到私有流的数据。
    virtual void SubscribePrivateTopic(THOST_TE_RESUME_TYPE nResumeType) = 0;

    /// 订阅公共流。
    ///@param nResumeType 公共流重传方式
    ///         THOST_TERT_RESTART:从本交易日开始重传
    ///         THOST_TERT_RESUME:从上次收到的续传
    ///         THOST_TERT_QUICK:只传送登录后公共流的内容
    ///@remark 该方法要在Init方法前调用。若不调用则不会收到公共流的数据。
    virtual void SubscribePublicTopic(THOST_TE_RESUME_TYPE nResumeType) = 0;

    /// 订阅交易所流控警告
    ///@remark 该方法必须在登录成功之后调用。
    virtual int SubscribeFlowCtrlWarning(char* ppTraderID[], int nCount) = 0;

    /// 取消订阅交易所流控警告
    ///@remark 该方法必须在登录成功之后调用。
    virtual int UnSubscribeFlowCtrlWarning(char* ppTraderID[], int nCount) = 0;

    /// 客户端认证请求
    virtual int ReqAuthenticate(CThostFtdcReqAuthenticateField* pReqAuthenticateField, int nRequestID) = 0;

    /// 用户登录请求
    virtual int ReqUserLogin(CThostFtdcReqUserLoginField* pReqUserLoginField, int nRequestID) = 0;

    /// 用户加密登录请求
    virtual int ReqUserLoginEncrypt(CThostFtdcReqUserLoginField* pReqUserLoginField, int nRequestID) = 0;

    /// 登出请求
    virtual int ReqUserLogout(CThostFtdcUserLogoutField* pUserLogout, int nRequestID) = 0;

    /// 报单录入请求
    virtual int ReqOrderInsert(CThostFtdcInputOrderField* pInputOrder, int nRequestID) = 0;

    /// 报单操作请求
    virtual int ReqOrderAction(CThostFtdcInputOrderActionField* pInputOrderAction, int nRequestID) = 0;

    /// 做市商批量报单操作请求
    virtual int ReqMKBatchOrderAction(CThostFtdcMKInputOrderActionField* pMKInputOrderAction, int nRequestID) = 0;

    /// 执行宣告录入请求
    virtual int ReqExecOrderInsert(CThostFtdcInputExecOrderField* pInputExecOrder, int nRequestID) = 0;

    /// 执行宣告操作请求
    virtual int ReqExecOrderAction(CThostFtdcInputExecOrderActionField* pInputExecOrderAction, int nRequestID) = 0;

    /// 询价录入请求
    virtual int ReqForQuoteInsert(CThostFtdcInputForQuoteField* pInputForQuote, int nRequestID) = 0;

    /// 报价录入请求
    virtual int ReqQuoteInsert(CThostFtdcInputQuoteField* pInputQuote, int nRequestID) = 0;

    /// 报价操作请求
    virtual int ReqQuoteAction(CThostFtdcInputQuoteActionField* pInputQuoteAction, int nRequestID) = 0;

    /// 批量报单操作请求
    virtual int ReqBatchOrderAction(CThostFtdcInputBatchOrderActionField* pInputBatchOrderAction, int nRequestID) = 0;

    /// 期权自对冲录入请求
    virtual int ReqOptionSelfCloseInsert(CThostFtdcInputOptionSelfCloseField* pInputOptionSelfClose, int nRequestID) = 0;

    /// 期权自对冲操作请求
    virtual int ReqOptionSelfCloseAction(CThostFtdcInputOptionSelfCloseActionField* pInputOptionSelfCloseAction, int nRequestID) = 0;

    /// 申请组合录入请求
    virtual int ReqCombActionInsert(CThostFtdcInputCombActionField* pInputCombAction, int nRequestID) = 0;

    /// 订阅资金变动推送
    virtual int ReqSubscribeFundChange(int nRequestID) = 0;

    /// 取消订阅资金变动推送
    virtual int ReqUnSubscribeFundChange(int nRequestID) = 0;

    /// 请求查询报单
    virtual int ReqQryOrder(CThostFtdcQryOrderField* pQryOrder, int nRequestID) = 0;

    /// 请求查询成交
    virtual int ReqQryTrade(CThostFtdcQryTradeField* pQryTrade, int nRequestID) = 0;

    /// 请求查询投资者持仓
    virtual int ReqQryInvestorPosition(CThostFtdcQryInvestorPositionField* pQryInvestorPosition, int nRequestID) = 0;

    /// 请求查询资金账户
    virtual int ReqQryTradingAccount(CThostFtdcQryTradingAccountField* pQryTradingAccount, int nRequestID) = 0;

    /// 请求查询投资者
    virtual int ReqQryInvestor(CThostFtdcQryInvestorField* pQryInvestor, int nRequestID) = 0;

    /// 请求查询交易编码
    virtual int ReqQryTradingCode(CThostFtdcQryTradingCodeField* pQryTradingCode, int nRequestID) = 0;

    /// 请求查询合约保证金率
    virtual int ReqQryInstrumentMarginRate(CThostFtdcQryInstrumentMarginRateField* pQryInstrumentMarginRate, int nRequestID) = 0;

    /// 请求查询合约手续费率
    virtual int ReqQryInstrumentCommissionRate(CThostFtdcQryInstrumentCommissionRateField* pQryInstrumentCommissionRate, int nRequestID) = 0;

    /// 请求查询交易所
    virtual int ReqQryExchange(CThostFtdcQryExchangeField* pQryExchange, int nRequestID) = 0;

    /// 请求查询产品
    virtual int ReqQryProduct(CThostFtdcQryProductField* pQryProduct, int nRequestID) = 0;

    /// 请求查询合约
    virtual int ReqQryInstrument(CThostFtdcQryInstrumentField* pQryInstrument, int nRequestID) = 0;

    /// 请求查询申请组合合约
    virtual int ReqQryCombInstrument(CThostFtdcQryCombInstrumentField* pQryCombInstrument, int nRequestID) = 0;

    /// 查询投资者RCAMS组合保证金
    virtual int ReqQryRCAMSInvestorProdMargin(CThostFtdcQryRCAMSInvestorProdMarginField* pQryRCAMSInvestorProdMargin, int nRequestID) = 0;

    /// 查询RCAMS策略组合持仓
    virtual int ReqQryRCAMSInvestorCombPosition(CThostFtdcQryRCAMSInvestorCombPositionField* pQryRCAMSInvestorCombPosition, int nRequestID) = 0;

    /// 请求单腿持仓汇总
    virtual int ReqQryInvestorPositionForComb(CThostFtdcQryInvestorPositionForCombField* pQryIPForComb, int nRequestID) = 0;

    /// 请求查询申请组合
    virtual int ReqQryCombAction(CThostFtdcQryCombActionField* pQryCombAction, int nRequestID) = 0;

    /// 请求查询行情
    virtual int ReqQryDepthMarketData(CThostFtdcQryDepthMarketDataField* pQryDepthMarketData, int nRequestID) = 0;

    /// 请求查询期权自对冲
    virtual int ReqQryOptionSelfClose(CThostFtdcQryOptionSelfCloseField* pQryOptionSelfClose, int nRequestID) = 0;

    /// 请求查询合约状态
    virtual int ReqQryInstrumentStatus(CThostFtdcQryInstrumentStatusField* pQryInstrumentStatus, int nRequestID) = 0;

    /// 请求查询投资者持仓明细
    /// 持仓明细不再维护，请勿以查询结果为准
    virtual int ReqQryInvestorPositionDetail(CThostFtdcQryInvestorPositionDetailField* pQryInvestorPositionDetail, int nRequestID) = 0;

    /// 请求查询交易所保证金率
    virtual int ReqQryExchangeMarginRate(CThostFtdcQryExchangeMarginRateField* pQryExchangeMarginRate, int nRequestID) = 0;

    /// 请求查询交易所调整保证金率
    virtual int ReqQryExchangeMarginRateAdjust(CThostFtdcQryExchangeMarginRateAdjustField* pQryExchangeMarginRateAdjust, int nRequestID) = 0;

    /// 请求查询期权交易成本
    virtual int ReqQryOptionInstrTradeCost(CThostFtdcQryOptionInstrTradeCostField* pQryOptionInstrTradeCost, int nRequestID) = 0;

    /// 请求查询期权合约手续费
    virtual int ReqQryOptionInstrCommRate(CThostFtdcQryOptionInstrCommRateField* pQryOptionInstrCommRate, int nRequestID) = 0;

    /// 请求查询执行宣告
    virtual int ReqQryExecOrder(CThostFtdcQryExecOrderField* pQryExecOrder, int nRequestID) = 0;

    /// 请求查询询价
    virtual int ReqQryForQuote(CThostFtdcQryForQuoteField* pQryForQuote, int nRequestID) = 0;

    /// 请求查询报价
    virtual int ReqQryQuote(CThostFtdcQryQuoteField* pQryQuote, int nRequestID) = 0;

    /// 请求查询申报费率
    virtual int ReqQryInstrumentOrderCommRate(CThostFtdcQryInstrumentOrderCommRateField* pQryInstrumentOrderCommRate, int nRequestID) = 0;

    /// 请求查询询价价差
    virtual int ReqQryForQuoteParam(CThostFtdcQryForQuoteParamField* pQryForQuoteParam, int nRequestID) = 0;

    /// 请求查询交易员报盘机
    virtual int ReqQryTraderOffer(CThostFtdcQryTraderOfferField* pQryTraderOffer, int nRequestID) = 0;

    /// 请求查询投资者SPBM品种明细
    virtual int ReqQryInvestorProdSPBMDetail(CThostFtdcQryInvestorProdSPBMDetailField* pQryInvestorProdSPBMDetail, int nRequestID) = 0;

    /// 请求查询投资者SPMM商品群保证金明细
    virtual int ReqQrySPMMInvestorCommodityGroupMargin(CThostFtdcQrySPMMInvestorCommodityGroupMarginField* pQrySPMMInvestorCommodityGroupMargin, int nRequestID) = 0;

    /// 请求查询投资者RULE保证金明细
    virtual int ReqQryRULEInvestorProdMargin(CThostFtdcQryRULEInvestorProdMarginField* pQryRULEInvestorProdMargin, int nRequestID) = 0;

    /// 请求查询系统功能开关设置
    virtual int ReqQryControlParam(CThostFtdcQryControlParamField* pQryControlParam, int nRequestID) = 0;

protected:
    ~CThostFtdcTraderApi() { };
};

#endif
